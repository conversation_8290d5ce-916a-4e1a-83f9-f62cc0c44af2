// General settings interfaces
interface DefaultTestCustomer {
  enabled: boolean;
  name: string;
  email: string;
}

interface EmailDomainRestriction {
  enabled: boolean;
  allowedDomains: string;
}

export interface GeneralConfig {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl: string;
  primaryColor: string;
  secondaryColor: string;
  footerText: string;
  enableCheckout: boolean;
  enableCustomCheckout: boolean;
  enableTestMode: boolean;
  defaultTestCustomer: DefaultTestCustomer;
  emailDomainRestriction: EmailDomainRestriction;
}

// In-memory configuration storage for demo
export const generalConfigStorage: GeneralConfig = {
  siteName: "TemplateHub Pro",
  siteDescription: "Premium productivity app templates and UI/UX design systems",
  logoUrl: "",
  faviconUrl: "",
  primaryColor: "#6366f1",
  secondaryColor: "#4f46e5",
  footerText: "© 2024 TemplateHub Pro",
  enableCheckout: true,
  enableCustomCheckout: true,
  enableTestMode: true,
  defaultTestCustomer: {
    enabled: true,
    name: "Test Designer",
    email: "<EMAIL>"
  },
  emailDomainRestriction: {
    enabled: false,
    allowedDomains: "gmail.com, hotmail.com, yahoo.com"
  }
};

/**
 * Get the general configuration from storage
 */
export function getGeneralConfig(): GeneralConfig {
  return generalConfigStorage;
}

/**
 * Update the general configuration in storage
 */
export function updateGeneralConfig(config: Partial<GeneralConfig>): GeneralConfig {
  Object.assign(generalConfigStorage, config);
  return generalConfigStorage;
}

/**
 * Get the allowed email domains as an array
 */
export function getAllowedEmailDomains(): string[] {
  const { enabled, allowedDomains } = generalConfigStorage.emailDomainRestriction;

  if (!enabled || !allowedDomains) {
    return [];
  }

  return allowedDomains
    .split(',')
    .map(domain => domain.trim())
    .filter(domain => domain.length > 0);
}

/**
 * Check if email domain restriction is enabled
 */
export function isEmailDomainRestrictionEnabled(): boolean {
  return generalConfigStorage.emailDomainRestriction.enabled;
}

/**
 * Get the default test customer
 */
export function getDefaultTestCustomer(): DefaultTestCustomer | null {
  const { enabled, name, email } = generalConfigStorage.defaultTestCustomer;

  if (!enabled) {
    return null;
  }

  return { enabled, name, email };
}

/**
 * Check if test mode is enabled
 */
export function isTestModeEnabled(): boolean {
  return generalConfigStorage.enableTestMode;
}

/**
 * Check if checkout is enabled
 */
export function isCheckoutEnabled(): boolean {
  return generalConfigStorage.enableCheckout;
}

/**
 * Check if custom checkout is enabled
 */
export function isCustomCheckoutEnabled(): boolean {
  return generalConfigStorage.enableCustomCheckout;
}

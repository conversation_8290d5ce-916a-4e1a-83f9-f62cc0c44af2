// Homepage configuration interfaces and storage
export interface HeroSection {
  title: string;
  subtitle: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  backgroundImage: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  textColor: string;
  showVideo: boolean;
  videoUrl: string;
}

export interface FeatureItem {
  id: string;
  icon: string;
  title: string;
  description: string;
  enabled: boolean;
}

export interface FeaturesSection {
  title: string;
  subtitle: string;
  features: FeatureItem[];
  layout: 'grid' | 'list' | 'carousel';
  columns: number;
}

export interface TestimonialItem {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  avatar: string;
  rating: number;
  enabled: boolean;
}

export interface TestimonialsSection {
  title: string;
  subtitle: string;
  testimonials: TestimonialItem[];
  layout: 'carousel' | 'grid';
  showRatings: boolean;
}

export interface ProductsSection {
  title: string;
  subtitle: string;
  showAllProducts: boolean;
  featuredProductIds: number[];
  layout: 'grid' | 'carousel';
  columns: number;
  showPrices: boolean;
  showDescriptions: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  enabled: boolean;
}

export interface FAQSection {
  title: string;
  subtitle: string;
  faqs: FAQItem[];
  layout: 'accordion' | 'tabs';
}

export interface CTASection {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
  backgroundType: 'image' | 'gradient' | 'solid';
  backgroundColor: string;
  backgroundImage: string;
  textColor: string;
}

export interface PageSection {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'products' | 'faq' | 'cta';
  title: string;
  enabled: boolean;
  order: number;
  content: HeroSection | FeaturesSection | TestimonialsSection | ProductsSection | FAQSection | CTASection;
}

export interface SEOSettings {
  title: string;
  description: string;
  keywords: string;
  ogTitle: string;
  ogDescription: string;
  ogImage: string;
  twitterTitle: string;
  twitterDescription: string;
  twitterImage: string;
}

export interface ThemeSettings {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  borderRadius: string;
  spacing: string;
}

export interface HomepageConfig {
  sections: PageSection[];
  seo: SEOSettings;
  theme: ThemeSettings;
  lastUpdated: string;
  version: number;
}

// Default homepage configuration
export const defaultHomepageConfig: HomepageConfig = {
  sections: [
    {
      id: 'hero-1',
      type: 'hero',
      title: 'Hero Section',
      enabled: true,
      order: 1,
      content: {
        title: 'Digital Invoice Generator',
        subtitle: 'Streamline Your Business',
        description: 'Generate professional PayPal invoices instantly for your digital products and services. Fast, secure, and reliable.',
        ctaText: 'Get Started',
        ctaLink: '#products',
        backgroundImage: '',
        backgroundType: 'gradient',
        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        textColor: '#ffffff',
        showVideo: false,
        videoUrl: ''
      } as HeroSection
    },
    {
      id: 'features-1',
      type: 'features',
      title: 'Features Section',
      enabled: true,
      order: 2,
      content: {
        title: 'Why Choose Our Platform',
        subtitle: 'Everything you need to manage your digital business',
        features: [
          {
            id: 'feature-1',
            icon: '⚡',
            title: 'Instant Invoice Generation',
            description: 'Create professional PayPal invoices in seconds with our automated system.',
            enabled: true
          },
          {
            id: 'feature-2',
            icon: '🔒',
            title: 'Secure Payments',
            description: 'All transactions are processed securely through PayPal\'s trusted platform.',
            enabled: true
          },
          {
            id: 'feature-3',
            icon: '📊',
            title: 'Analytics Dashboard',
            description: 'Track your sales, customers, and revenue with detailed analytics.',
            enabled: true
          },
          {
            id: 'feature-4',
            icon: '🎨',
            title: 'Custom Checkout Pages',
            description: 'Create beautiful, branded checkout experiences for your customers.',
            enabled: true
          },
          {
            id: 'feature-5',
            icon: '📧',
            title: 'Email Automation',
            description: 'Automated email notifications for invoices, payments, and follow-ups.',
            enabled: true
          },
          {
            id: 'feature-6',
            icon: '🌐',
            title: 'Global Reach',
            description: 'Accept payments from customers worldwide with multi-currency support.',
            enabled: true
          }
        ],
        layout: 'grid',
        columns: 3
      } as FeaturesSection
    },
    {
      id: 'products-1',
      type: 'products',
      title: 'Products Section',
      enabled: true,
      order: 3,
      content: {
        title: 'Our Digital Products',
        subtitle: 'Choose from our selection of premium digital services',
        showAllProducts: true,
        featuredProductIds: [],
        layout: 'grid',
        columns: 3,
        showPrices: true,
        showDescriptions: true
      } as ProductsSection
    },
    {
      id: 'cta-1',
      type: 'cta',
      title: 'Call to Action',
      enabled: true,
      order: 4,
      content: {
        title: 'Ready to Get Started?',
        description: 'Join thousands of businesses already using our platform to streamline their invoice generation.',
        primaryButtonText: 'Start Free Trial',
        primaryButtonLink: '#products',
        secondaryButtonText: 'Learn More',
        secondaryButtonLink: '#features',
        backgroundType: 'solid',
        backgroundColor: '#f8fafc',
        backgroundImage: '',
        textColor: '#1e293b'
      } as CTASection
    }
  ],
  seo: {
    title: 'Digital Invoice Generator - PayPal Integration',
    description: 'Generate professional PayPal invoices instantly for your digital products. Fast, secure, and reliable invoice management platform.',
    keywords: 'paypal invoice, digital products, invoice generator, payment processing, business automation',
    ogTitle: 'Digital Invoice Generator - PayPal Integration',
    ogDescription: 'Generate professional PayPal invoices instantly for your digital products. Fast, secure, and reliable.',
    ogImage: '',
    twitterTitle: 'Digital Invoice Generator',
    twitterDescription: 'Generate professional PayPal invoices instantly for your digital products.',
    twitterImage: ''
  },
  theme: {
    primaryColor: '#0070ba',
    secondaryColor: '#003087',
    accentColor: '#009cde',
    backgroundColor: '#ffffff',
    textColor: '#1e293b',
    fontFamily: 'Inter, system-ui, sans-serif',
    borderRadius: '8px',
    spacing: '1rem'
  },
  lastUpdated: new Date().toISOString(),
  version: 1
};

// In-memory storage for homepage configuration
export let homepageConfigStorage: HomepageConfig = { ...defaultHomepageConfig };

/**
 * Get the homepage configuration
 */
export function getHomepageConfig(): HomepageConfig {
  return homepageConfigStorage;
}

/**
 * Update the homepage configuration
 */
export function updateHomepageConfig(config: Partial<HomepageConfig>): HomepageConfig {
  homepageConfigStorage = {
    ...homepageConfigStorage,
    ...config,
    lastUpdated: new Date().toISOString(),
    version: homepageConfigStorage.version + 1
  };
  return homepageConfigStorage;
}

/**
 * Update a specific section
 */
export function updateSection(sectionId: string, updates: Partial<PageSection>): HomepageConfig {
  const sectionIndex = homepageConfigStorage.sections.findIndex(s => s.id === sectionId);
  if (sectionIndex !== -1) {
    homepageConfigStorage.sections[sectionIndex] = {
      ...homepageConfigStorage.sections[sectionIndex],
      ...updates
    };
    homepageConfigStorage.lastUpdated = new Date().toISOString();
    homepageConfigStorage.version += 1;
  }
  return homepageConfigStorage;
}

/**
 * Add a new section
 */
export function addSection(section: PageSection): HomepageConfig {
  homepageConfigStorage.sections.push(section);
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Remove a section
 */
export function removeSection(sectionId: string): HomepageConfig {
  homepageConfigStorage.sections = homepageConfigStorage.sections.filter(s => s.id !== sectionId);
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Reorder sections
 */
export function reorderSections(sectionIds: string[]): HomepageConfig {
  const reorderedSections = sectionIds.map((id, index) => {
    const section = homepageConfigStorage.sections.find(s => s.id === id);
    if (section) {
      return { ...section, order: index + 1 };
    }
    return null;
  }).filter(Boolean) as PageSection[];

  homepageConfigStorage.sections = reorderedSections;
  homepageConfigStorage.lastUpdated = new Date().toISOString();
  homepageConfigStorage.version += 1;
  return homepageConfigStorage;
}

/**
 * Reset to default configuration
 */
export function resetHomepageConfig(): HomepageConfig {
  homepageConfigStorage = { ...defaultHomepageConfig };
  return homepageConfigStorage;
}
